import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { Form, useActionData, useLoaderData, useNavigation } from '@remix-run/react';
import { useUser } from '~/utils/client-utils';
import { db } from '~/utils/db.server';
import { useState } from 'react';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

// Type definition for migration object
type Migration = {
	name: string;
	status: string;
	applied_at: Date | null | undefined;
	error_message: string | null | undefined;
};

const execPromise = promisify(exec);

// Function to get all migration files from the db/migrations directory
async function getMigrationFiles() {
	const migrationsDir = path.join(process.cwd(), 'db', 'migrations');
	const entries = await fs.promises.readdir(migrationsDir, { withFileTypes: true });

	// Filter directories and sort by name (which should be timestamp_name format)
	return entries
		.filter(entry => entry.isDirectory())
		.map(dir => dir.name)
		.sort();
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
	// Get all migration files
	const migrationFiles = await getMigrationFiles();

	// Get all tracked migrations from the database
	const trackedMigrations = await db.migration_tracking.findMany({
		orderBy: { migration_name: 'asc' }
	});

	// Create a map of tracked migrations for easy lookup
	const trackedMigrationsMap = new Map(trackedMigrations.map(migration => [migration.migration_name, migration]));

	// Combine the data
	const migrations = migrationFiles.map(fileName => {
		const tracked = trackedMigrationsMap.get(fileName);
		return {
			name: fileName,
			status: tracked?.status || 'pending',
			applied_at: tracked?.applied_at,
			error_message: tracked?.error_message
		};
	});

	return json({ migrations });
};

export const action = async ({ request }: ActionFunctionArgs) => {
	const formData = await request.formData();
	const action = formData.get('action');

	if (action === 'scan') {
		// Just reload the page to scan for new migrations
		return redirect('/admin/system/migrations');
	}

	if (action === 'run') {
		const migrationName = formData.get('migrationName')?.toString();
		if (!migrationName) {
			return json({ error: 'Migration name is required' });
		}

		try {
			// Check if migration is already tracked
			const existingMigration = await db.migration_tracking.findUnique({
				where: { migration_name: migrationName }
			});

			if (existingMigration && ['completed', 'skipped'].includes(existingMigration.status)) {
				return json({
					success: false,
					message: `Migration ${migrationName} is already ${existingMigration.status}`
				});
			}

			// Run the migration using db migrate
			const { stdout, stderr } = await execPromise(`npx db migrate resolve --applied ${migrationName}`);

			// Update or create the migration tracking record
			await db.migration_tracking.upsert({
				where: { migration_name: migrationName },
				update: {
					status: 'completed',
					applied_at: new Date(),
					error_message: stderr || null
				},
				create: {
					migration_name: migrationName,
					status: 'completed',
					applied_at: new Date(),
					error_message: stderr || null
				}
			});

			return json({
				success: true,
				message: `Migration ${migrationName} applied successfully`,
				output: stdout
			});
		} catch (error) {
			// Properly type the error
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';

			// Update or create the migration tracking record with error
			await db.migration_tracking.upsert({
				where: { migration_name: migrationName },
				update: {
					status: 'failed',
					applied_at: new Date(),
					error_message: errorMessage
				},
				create: {
					migration_name: migrationName,
					status: 'failed',
					applied_at: new Date(),
					error_message: errorMessage
				}
			});

			return json({
				success: false,
				message: `Failed to apply migration ${migrationName}`,
				error: errorMessage
			});
		}
	}

	if (action === 'skip') {
		const migrationName = formData.get('migrationName')?.toString();
		if (!migrationName) {
			return json({ error: 'Migration name is required' });
		}

		// Update or create the migration tracking record as skipped
		await db.migration_tracking.upsert({
			where: { migration_name: migrationName },
			update: {
				status: 'skipped',
				applied_at: new Date()
			},
			create: {
				migration_name: migrationName,
				status: 'skipped',
				applied_at: new Date()
			}
		});

		return json({
			success: true,
			message: `Migration ${migrationName} marked as skipped`
		});
	}

	return json({ error: 'Invalid action' });
};

export default function DatabaseMigrationsPage() {
	const user = useUser();
	if (user.role !== 'Admin') {
		throw new Error('No permission.');
	}

	const data = useLoaderData<typeof loader>();
	const actionData = useActionData<typeof action>();
	const navigation = useNavigation();
	const [selectedMigration, setSelectedMigration] = useState<string | null>(null);

	const isSubmitting = navigation.state === 'submitting';

	// Function to get status badge color
	const getStatusColor = (status: string) => {
		switch (status) {
			case 'completed':
				return 'bg-green-100 text-green-800';
			case 'failed':
				return 'bg-red-100 text-red-800';
			case 'skipped':
				return 'bg-yellow-100 text-yellow-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	return (
		<div className="p-8">
			<div className="mb-8 flex items-center justify-between">
				<h1 className="text-3xl font-bold">Database Migrations</h1>
				<Form method="post">
					<input type="hidden" name="action" value="scan" />
					<button
						type="submit"
						className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
						disabled={isSubmitting}
					>
						{isSubmitting && navigation.formData?.get('action') === 'scan'
							? 'Scanning...'
							: 'Scan for New Migrations'}
					</button>
				</Form>
			</div>

			{actionData?.message && (
				<div
					className={`mb-4 rounded p-4 ${actionData.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
				>
					{actionData.message}
					{actionData.error && (
						<div className="mt-2 text-sm">
							<strong>Error:</strong> {actionData.error}
						</div>
					)}
				</div>
			)}

			<div className="overflow-hidden bg-white shadow sm:rounded-md">
				<ul className="divide-y divide-gray-200">
					{data.migrations.length === 0 ? (
						<li className="px-6 py-4 text-center text-gray-500">No migrations found</li>
					) : (
						data.migrations.map((migration: Migration) => (
							<li key={migration.name} className="px-6 py-4">
								<div className="flex items-center justify-between">
									<div>
										<div className="flex items-center">
											<span className="font-medium">{migration.name}</span>
											<span
												className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(migration.status)}`}
											>
												{migration.status}
											</span>
										</div>
										{migration.applied_at && (
											<div className="text-sm text-gray-500">
												Applied: {new Date(migration.applied_at).toLocaleString()}
											</div>
										)}
										{migration.error_message && (
											<div className="mt-2 text-sm text-red-600">
												<strong>Error:</strong> {migration.error_message}
											</div>
										)}
									</div>
									<div className="flex space-x-2">
										{migration.status === 'pending' && (
											<>
												<Form method="post">
													<input type="hidden" name="action" value="run" />
													<input type="hidden" name="migrationName" value={migration.name} />
													<button
														type="submit"
														className="rounded bg-green-500 px-3 py-1 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
														disabled={isSubmitting}
													>
														{isSubmitting &&
														navigation.formData?.get('migrationName') === migration.name &&
														navigation.formData?.get('action') === 'run'
															? 'Running...'
															: 'Run'}
													</button>
												</Form>
												<Form method="post">
													<input type="hidden" name="action" value="skip" />
													<input type="hidden" name="migrationName" value={migration.name} />
													<button
														type="submit"
														className="rounded bg-yellow-500 px-3 py-1 text-white hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
														disabled={isSubmitting}
													>
														{isSubmitting &&
														navigation.formData?.get('migrationName') === migration.name &&
														navigation.formData?.get('action') === 'skip'
															? 'Skipping...'
															: 'Skip'}
													</button>
												</Form>
											</>
										)}
										{migration.status === 'failed' && (
											<Form method="post">
												<input type="hidden" name="action" value="run" />
												<input type="hidden" name="migrationName" value={migration.name} />
												<button
													type="submit"
													className="rounded bg-green-500 px-3 py-1 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
													disabled={isSubmitting}
												>
													{isSubmitting &&
													navigation.formData?.get('migrationName') === migration.name &&
													navigation.formData?.get('action') === 'run'
														? 'Retrying...'
														: 'Retry'}
												</button>
											</Form>
										)}
									</div>
								</div>
							</li>
						))
					)}
				</ul>
			</div>
		</div>
	);
}
